import base64
import json
import time
import unittest
from typing import Any, cast
from unittest.mock import ANY
from uuid import uuid4

import braintrust
import openai
import requests
import sseclient
from braintrust.db_fields import TRANSACTION_ID_FIELD
from braintrust.functions.stream import BraintrustStream
from braintrust.xact_ids import prettify_xact
from braintrust_local.api_db_util import get_object_json
from braintrust_local.util import get_from_path
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, TEST_ARGS, BraintrustAppTestBase, make_v1_url

SPAN_ID_HEADER = "x-bt-span-id"

CALCULATOR_CODE_TS = """
async function handler({
    op,
    a,
    b,
  }: {
    op: "add" | "subtract" | "multiply" | "divide";
    a: number;
    b: number;
  }) {
    switch (op) {
      case "add":
        return a + b;
      case "subtract":
        return a - b;
      case "multiply":
        return a * b;
      case "divide":
        return a / b;
      default:
        throw new Error("Invalid operation");
    }
  }
"""

CALCULATOR_CODE_PY = """
def handler(op, a, b):
    if op == "add":
        return a + b
    elif op == "subtract":
        return a - b
    elif op == "multiply":
        return a * b
    elif op == "divide":
        return a / b
    else:
        raise ValueError(f"Invalid operation: {op}")
"""

CALCULATOR_PARAMS = {
    "type": "object",
    "properties": {
        "op": {"type": "string", "enum": ["add", "subtract", "multiply", "divide"]},
        "a": {"type": "number"},
        "b": {"type": "number"},
    },
}


class FunctionTestBase(BraintrustAppTestBase):
    def _get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def _insert_function(self, **kwargs):
        return self.run_request("post", make_v1_url("function"), json=kwargs).json()

    def _update_function(self, id, **kwargs):
        return self.run_request("patch", make_v1_url("function", id=id), json=kwargs).json()

    def _load_function(self, **kwargs):
        return self.run_request("get", make_v1_url("function"), params=kwargs).json()

    def _openai_client(self):
        return braintrust.wrap_openai(
            openai.OpenAI(
                base_url=self.openai_base_url,
                api_key=self.org_api_key,
            )
        )

    def _insert_calculator(self, lang):
        assert lang in ["typescript", "python"]
        return self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "code",
                "data": {
                    "type": "inline",
                    "runtime_context": {"runtime": "node", "version": "20"}
                    if lang == "typescript"
                    else {
                        "runtime": "python",
                        "version": "3.11",
                    },
                    "code": CALCULATOR_CODE_TS if lang == "typescript" else CALCULATOR_CODE_PY,
                },
            },
            function_schema={
                "parameters": CALCULATOR_PARAMS,
                "returns": {"type": "number"},
            },
            name="Calculator",
            slug="calculator",
        )

    def setUp(self):
        super().setUp()
        self.logger = braintrust.init_logger(project="function test")
        self.project = self.logger.project

        self.proxy_url = braintrust.logger._state.proxy_url
        self.openai_base_url = f"{self.proxy_url}/v1"

    def tearDown(self):
        if TEST_ARGS.get("update"):
            requests.get(f"{self.proxy_url}/proxy/dump-cache")
        super().tearDown()


class FunctionTest(FunctionTestBase):
    def test_basic_create(self):
        slug = "my-prompt"
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                    ],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug=slug,
        )

        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(function_record["id"], prompt.id)

        # Run it via the proxy
        # Note: this is approximately, but not exactly, what the function call runs. If you
        # delete the proxy cache and re-run this, it may fail if the answers stop lining up.
        client = self._openai_client()
        resp = client.chat.completions.create(stream=True, **prompt.build(formula="1+1"))
        answer = ""
        for message in resp:
            if message.choices[0].delta.content:
                answer += message.choices[0].delta.content

        # Run it as a function
        resp = braintrust.invoke(project_name=self.project.name, slug=slug, input={"formula": "1+1"})
        self.assertEqual(answer, resp)

    def test_global_function(self):
        output = "food"
        expected = "fool"

        resp = braintrust.invoke(global_function="Levenshtein", input=dict(output=output, expected=expected))
        score = resp["score"]
        self.assertEqual(0.75, score)

        # Run with streaming
        resp = braintrust.invoke(
            global_function="Levenshtein", input=dict(output=output, expected=expected), stream=True
        )
        result = resp.final_value()
        self.assertEqual(score, result["score"])

    def test_large_request_body(self):
        output = "a" * (10 * 1024 * 1024)  # 10MB string
        expected = output

        resp = braintrust.invoke(global_function="Levenshtein", input=dict(output=output, expected=expected))
        score = resp["score"]
        self.assertEqual(1, score)

        # Run with streaming
        resp = braintrust.invoke(
            global_function="Levenshtein", input=dict(output=output, expected=expected), stream=True
        )
        result = resp.final_value()
        self.assertEqual(score, result["score"])

    @parameterized.expand(
        [
            (object_type, call_method, streaming, use_pretty_xact)
            for object_type in ["prompt", "function", "playground"]
            for call_method in ["id", "name"]
            for streaming in [False, True]
            for use_pretty_xact in [False, True]
            if not (object_type == "playground" and call_method == "name")
        ]
    )
    def test_function_versions(self, mode, call_method, streaming, use_pretty_xact):
        slug = f"prompt-version-{uuid4()}"

        if mode == "playground":
            playground_meta = self.run_request(
                "post",
                f"{LOCAL_APP_URL}/api/prompt_session/register",
                json=dict(
                    org_name=self.org_name,
                    project_name=self.project.name,
                    session_name="foo",
                ),
            ).json()
            prompt_session_id = playground_meta["id"]
        else:
            prompt_session_id = None

        def upload_prompt(id, messages):
            extra_kwargs = {}
            if mode == "function":
                extra_kwargs["function_data"] = {"type": "prompt"}

            if mode == "playground":
                if id:
                    extra_kwargs["id"] = id

                resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/logs3",
                    json=dict(
                        api_version=2,
                        rows=[
                            dict(
                                prompt_session_id=prompt_session_id,
                                prompt_data={
                                    "prompt": {
                                        "type": "chat",
                                        "messages": messages,
                                    },
                                    "options": {
                                        "model": "gpt-3.5-turbo",
                                    },
                                },
                                **extra_kwargs,
                            )
                        ],
                    ),
                )
                result = resp.json()
                return result["ids"][0], result["xact_id"]
            else:
                function_resp = self.run_request(
                    "post",
                    f"{LOCAL_API_URL}/v1/{mode}",
                    json=dict(
                        project_id=self.project.id,
                        prompt_data={
                            "prompt": {
                                "type": "chat",
                                "messages": messages,
                            },
                            "options": {
                                "model": "gpt-3.5-turbo",
                            },
                        },
                        name=slug,
                        slug=slug,
                        **extra_kwargs,
                    ),
                )

                function_record = function_resp.json()
                id = function_record["id"]
                version = function_record["_xact_id"]
                return id, version

        id, version = upload_prompt(
            None, [{"role": "user", "content": "Just return the string 'a' (without quotes). Nothing else"}]
        )

        def make_call_args(version=None, parent=None):
            args = {}
            if version:
                args["version"] = prettify_xact(version) if use_pretty_xact else version
            if parent:
                args["parent"] = parent

            if call_method == "id" and mode == "playground":
                args["prompt_session_id"] = prompt_session_id
                args["prompt_session_function_id"] = id
            elif call_method == "id":
                args["function_id"] = id
            else:
                args["project_name"] = self.project.name
                args["slug"] = slug

            return args

        def call_function(expect_cached, version=None, parent=None, expect_error=False):
            # Use the raw function calling HTTP endpoint so we can look at the headers
            resp = self.run_request(
                "post",
                f"{self.proxy_url}/function/invoke",
                json={
                    **make_call_args(version, parent),
                    "stream": streaming,
                },
                expect_error=expect_error,
            )

            if not resp.ok:
                assert expect_error
                return resp.text

            cached = resp.headers.get("x-bt-function-meta-cached")
            self.assertIsNotNone(cached)
            cached = cached.lower()
            if expect_cached:
                self.assertEqual("hit", cached)
            else:
                self.assertEqual("miss", cached)

            # If the result itself is cached, the credentials should be too
            if expect_cached:
                creds_cached = resp.headers.get("x-bt-function-creds-cached")
                self.assertIsNotNone(creds_cached)
                creds_cached = creds_cached.lower()
                self.assertEqual("hit", creds_cached)

            if streaming:
                return parse_streaming_function_call(resp)
            else:
                return resp.json()

        initial_call = call_function(False, version=None)
        self.assertEqual("a", initial_call)
        next_call = call_function(True, version=None)
        self.assertEqual("a", next_call)
        with_version = call_function(False, version=version)
        self.assertEqual(initial_call, with_version)
        with_version_2 = call_function(True, version=version)
        self.assertEqual(with_version, with_version_2)

        # Call it through the REST API format. NOTE: we still call this
        # through the test-proxy, but the same endpoint is available on
        # the API server
        if mode != "playground":
            resp = self.run_request(
                "post",
                f"{self.proxy_url}/v1/function/{id}/invoke",
                json={},
            )
            self.assertEqual("a", resp.json())

        # Now, update the function
        new_id, new_version = upload_prompt(
            id, [{"role": "user", "content": "Just return the string 'b' (without quotes). Nothing else"}]
        )
        self.assertEqual(id, new_id)

        no_version = call_function(False, version=None)
        self.assertEqual("b", no_version)
        no_version = call_function(True, version=None)
        self.assertEqual("b", no_version)
        with_new_version = call_function(False, version=new_version)
        self.assertEqual(no_version, with_new_version)

        with_old_version = call_function(True, version=version)
        self.assertEqual("a", with_old_version)

        # Make sure the version argument works in the REST API
        if mode != "playground":
            resp = self.run_request(
                "post",
                f"{self.proxy_url}/v1/function/{id}/invoke",
                json={"version": version},
            )
            self.assertEqual("a", resp.json())

        # Now call the function without logging
        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/call",
            json={
                **make_call_args(parent=None),
                "stream": streaming,
            },
        )
        self.assertIsNone(resp.headers.get(SPAN_ID_HEADER))

        # Now try logging the request, and make sure that it's logged and cached
        logger = braintrust.init_logger(project=self.project.name)
        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/call",
            json={
                **make_call_args(parent=logger.export()),
                "stream": streaming,
            },
        )
        span_id = resp.headers.get(SPAN_ID_HEADER)
        self.assertIsNotNone(span_id)

        # Try logging into the project via project id
        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/call",
            json={
                **make_call_args(parent={"object_type": "project_logs", "object_id": self.project.id}),
                "stream": streaming,
            },
        )
        span_id = resp.headers.get(SPAN_ID_HEADER)
        self.assertIsNotNone(span_id)

        # Access the log entry and its child LLM call through the API
        # It can take a little bit of time to show up in the logs. We'll give
        # it up to a minute.
        start = time.time()
        while time.time() - start < 60:
            resp = self.run_request(
                "post",
                f"{LOCAL_API_URL}/btql",
                json={"query": f"from: project_logs('{self.project.id}') traces | select: * | filter: id='{span_id}'"},
            )
            data = resp.json()
            if len(data["data"]) >= 2:
                break
            else:
                time.sleep(0.1)
        else:
            self.fail("Log entry not found")
        # When you query by id, we return all spans with that root span id automatically
        self.assertEqual(len(data["data"]), 2)

        root_span_id = data["data"][0]["root_span_id"]
        for entry in resp.json()["data"]:
            self.assertEqual(entry["root_span_id"], root_span_id)
            if entry["span_id"] == root_span_id:
                continue

            self.assertEqual(entry["span_attributes"]["type"], "llm")
            self.assertEqual(entry["metrics"]["cached"], 1)

        # Finally try creating an invalid prompt
        new_id, new_version = upload_prompt(id, messages=[])
        resp = call_function(expect_cached=False, expect_error=True)
        self.assertIsInstance(resp, str)
        self.assertTrue("empty array" in resp or "No API keys" in resp, resp)

    @parameterized.expand([(call_method, streaming) for call_method in ["id", "name"] for streaming in [False, True]])
    def test_custom_scorer(self, call_method, streaming):
        slug = f"prompt-version-{uuid4()}"

        function_resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/function",
            json=dict(
                project_id=self.project.id,
                prompt_data={
                    "prompt": {"type": "chat", "messages": [{"role": "user", "content": "Always pick choice A"}]},
                    "options": {
                        "model": "gpt-4o",
                    },
                    "parser": {
                        "type": "llm_classifier",
                        "use_cot": True,
                        "choice_scores": {
                            "A": 0.1,
                            "B": 1,
                        },
                    },
                },
                name=slug,
                slug=slug,
                function_data={"type": "prompt"},
                function_type="scorer",
            ),
        )

        function_record = function_resp.json()
        id = function_record["id"]

        call_args = {}
        if call_method == "id":
            call_args["function_id"] = id
        else:
            call_args["project_name"] = self.project.name
            call_args["slug"] = slug

        if streaming:
            call_args["stream"] = streaming

        resp = self.run_request("post", f"{self.proxy_url}/function/invoke", json=call_args)

        if streaming:
            result = parse_streaming_function_call(resp)
        else:
            result = resp.json()

        print(result)
        self.assertEqual(result["score"], 0.1)
        self.assertEqual(result["metadata"]["choice"], "A")
        self.assertIsNotNone(result["metadata"]["rationale"])

        # Retrieve all scorer functions and make sure it's included

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"from: project_functions('{self.project.id}') | select: * | filter: function_type='scorer'"
            },
        ).json()
        self.assertEqual(len(resp["data"]), 1)
        self.assertEqual(resp["data"][0]["id"], id)

    def test_invalid_scorer(self):
        slug = f"prompt-version-{uuid4()}"
        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/function",
            json=dict(
                project_id=self.project.id,
                prompt_data={
                    "prompt": {"type": "chat", "messages": [{"role": "user", "content": "Always pick choice A"}]},
                    "options": {
                        "model": "gpt-4o",
                    },
                    "parser": {
                        "type": "llm_classifier",
                        "use_cot": True,
                        "choice_scores": {
                            "A": 0.1,
                            "B": 1.5,  # We block scores outside of [0,1]
                        },
                    },
                },
                name=slug,
                slug=slug,
                function_data={"type": "prompt"},
                function_type="scorer",
            ),
            expect_error=True,
        )

    @parameterized.expand([(streaming,) for streaming in [False, True]])
    def test_structured_output(self, streaming):
        slug = "structured-output"
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                    ],
                },
                "options": {
                    "model": "gpt-4o",
                    "params": {
                        "response_format": {
                            "type": "json_schema",
                            "json_schema": {
                                "name": "result",
                                "schema": {
                                    "type": "object",
                                    "properties": {"result": {"type": "number"}},
                                    "additionalProperties": False,
                                    "required": ["result"],
                                },
                            },
                        },
                    },
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug=slug,
        )

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/invoke",
            json={
                "project_name": self.project.name,
                "slug": slug,
                "input": {"formula": "1+1"},
                "stream": streaming,
            },
        )
        if streaming:
            result = parse_streaming_function_call(resp)
        else:
            result = resp.json()
        self.assertEqual(result, {"result": 2})

    @parameterized.expand([(streaming, use_cache) for streaming in [False, True] for use_cache in [False, True]])
    def test_use_cache(self, streaming, use_cache):
        slug = "structured-output"
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                    ],
                },
                "options": {
                    "model": "gpt-4o",
                    "params": {
                        "temperature": 0,
                        "use_cache": use_cache,
                    },
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug=slug,
        )

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/invoke",
            json={
                "project_name": self.project.name,
                "slug": slug,
                "input": {"formula": "1+1"},
                "stream": streaming,
            },
            # This "hacks" around the fact that when running tests, we don't have an API key
            # set, so any uncached requests will fail.
            expect_error=not use_cache,
        )
        if not use_cache:
            return

        if streaming:
            result = parse_streaming_function_call(resp)
        else:
            result = resp.json()
        self.assertEqual(result, "2")

    def test_eval(self):
        dataset = braintrust.init_dataset("dataset-project", "dataset-name")
        for i in range(10):
            dataset.insert(input=i, output=f"output {i}")
        braintrust.flush()

        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Return exactly the following string: output {{input}}"}],
                },
                "options": {
                    "model": "gpt-4o",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug="echo-prompt",
        )

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": self.project.id,
                "data": {"project_name": "dataset-project", "dataset_name": "dataset-name"},
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [
                    {"global_function": "Levenshtein"},
                    {"global_function": "Factuality"},
                ],
            },
        )

        summary = resp.json()

        self.assertEqual(summary["projectName"], self.project.name)
        self.assertAlmostEqual(summary["scores"]["Levenshtein"]["score"], 1.0)
        self.assertAlmostEqual(summary["scores"]["Factuality"]["score"], 1.0)

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": self.project.id,
                "data": {"dataset_id": dataset.id},
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [
                    {"global_function": "Levenshtein"},
                    {"global_function": "Factuality"},
                ],
            },
        )
        summary_dataset_id = resp.json()
        self.assertEqual(
            summary_dataset_id["scores"]["Levenshtein"]["score"], summary["scores"]["Levenshtein"]["score"]
        )
        self.assertEqual(summary_dataset_id["scores"]["Factuality"]["score"], summary["scores"]["Factuality"]["score"])

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/v1/eval",
            json={
                "project_id": self.project.id,
                "data": {"project_name": "dataset-project", "dataset_name": "dataset-name"},
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [
                    {"global_function": "Levenshtein"},
                    {"global_function": "Factuality"},
                ],
            },
        )
        summary_api = resp.json()
        self.assertEqual(summary_api["scores"]["Levenshtein"]["score"], summary["scores"]["Levenshtein"]["score"])
        self.assertEqual(summary_api["scores"]["Factuality"]["score"], summary["scores"]["Factuality"]["score"])

    def test_strict_mode(self):
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "{{foo.bar}}"}],
                },
                "options": {
                    "model": "gpt-4o",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug="echo-prompt",
        )

        for strict in [True, False]:
            for valid in [True, False]:
                print(f"strict: {strict}, valid: {valid}")
                if valid:
                    args = {"foo": {"bar": "baz"}}
                else:
                    args = {"foo": {"baz": "bar"}}
                try:
                    braintrust.invoke(
                        function_id=function_record["id"],
                        input=args,
                        strict=strict,
                    )
                    self.assertTrue(valid or not strict)
                except Exception as e:
                    self.assertTrue(strict and not valid)

        dataset = braintrust.init_dataset("dataset-project", "dataset-name")
        dataset.insert(input={"foo": {"bar": "baz"}}, output="output")
        dataset.insert(input={"foo": {"baz": "bar"}}, output="output")
        braintrust.flush()

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": self.project.id,
                "data": {"project_name": "dataset-project", "dataset_name": "dataset-name"},
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [
                    {"global_function": "Levenshtein"},
                    {"global_function": "Factuality"},
                ],
                "strict": True,
            },
        ).json()

        spans = [x for x in get_object_json("experiment", resp["experimentId"]) if x["is_root"]]
        self.assertEqual(len(spans), 2)
        self.assertEqual(len([x for x in spans if x["error"]]), 1)

    def test_prompt_rendering(self):
        scorer_slug = f"scorer-{uuid4()}"

        function_resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/function",
            json=dict(
                project_id=self.project.id,
                prompt_data={
                    "prompt": {
                        "type": "chat",
                        "messages": [
                            {
                                "role": "user",
                                "content": """
                                    Return "A" if `answer.value` is equal to {{expected.answer.value}}, and "B" otherwise.

                                    Ignore the following:

                                    ```yaml
                                    input: {{input}}
                                    output: {{output}}
                                    expected: {{expected}}
                                    metadata: {{metadata}}
                                """.strip(),
                            }
                        ],
                    },
                    "options": {
                        "model": "gpt-4o-mini",
                    },
                    "parser": {
                        "type": "llm_classifier",
                        "use_cot": False,
                        "choice_scores": {
                            "A": 1,
                            "B": 0,
                        },
                    },
                },
                name=scorer_slug,
                slug=scorer_slug,
                function_data={"type": "prompt"},
                function_type="scorer",
            ),
        )

        scorer = function_resp.json()

        self.assertIsNotNone(scorer["id"])

        dataset_slug = f"dataset-{uuid4()}"

        dataset = braintrust.init_dataset(self.project.name, dataset_slug)
        datum = cast(
            Any,
            dict(
                input={"left": 2, "right": 1, "operator": "+"},
                expected={
                    "input": {"left": 4, "right": 2, "operator": "/"},
                    "answer": {"value": 3},
                    "expected": 2,
                    "metadata": 3,
                },
                metadata={"input": 3, "expected": 3, "metadata": 3, "operator": "-"},
            ),
        )
        dataset.insert(**datum)

        braintrust.flush()

        calculator_slug = f"calculator-{uuid4()}"

        prompt = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {
                            "role": "user",
                            "content": """
                                You are a calculator. What is: {{left}} {{operator}} {{right}}.

                                Provide the answer in the following yaml (replace YOUR_ANSWER and leave all other props as is):

                                ```yaml
                                answer:
                                    value: YOUR_ANSWER
                                state:
                                    input: {{input}}
                                    left: {{left}}
                                    right: {{right}}
                                    operator: {{operator}}
                                    input.left: {{input.left}}
                                    input.right: {{input.right}}
                                    input.operator: {{input.operator}}
                                    expected: {{expected}}
                                    expected.answer.value: {{expected.answer.value}}
                                    metadata: {{metadata}}
                                    metadata.operator: {{metadata.operator}}
                                ```

                                Convert the yaml to json and only provide the json in your response (do not use markdown).
                            """.strip(),
                        }
                    ],
                },
                "options": {
                    "model": "gpt-4o-mini",
                },
            },
            function_data={
                "type": "prompt",
            },
            name=calculator_slug,
            slug=calculator_slug,
        )

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": self.project.id,
                "data": {"project_name": self.project.name, "dataset_name": dataset_slug},
                "task": {
                    "function_id": prompt["id"],
                },
                "scores": [
                    {"function_id": scorer["id"]},
                ],
            },
        )

        summary = resp.json()
        self.assertEqual(summary["scores"][scorer["slug"]]["score"], 1)

        spans = get_object_json("experiment", summary["experimentId"])

        prompt_span = next((span for span in spans if get_from_path(span, "metadata.prompt.id") == prompt["id"]))
        scorer_span = next((span for span in spans if get_from_path(span, "metadata.prompt.id") == scorer["id"]))

        prompt_output = prompt_span["output"][0]["message"]["content"]

        self.assertEqual(
            json.loads(prompt_output),
            {
                "answer": {"value": 3},
                "state": {
                    "expected": datum["expected"],
                    "expected.answer.value": datum["expected"]["answer"]["value"],
                    "input": datum["input"],
                    "input.left": datum["input"]["left"],
                    "input.operator": datum["input"]["operator"],
                    "input.right": datum["input"]["right"],
                    "left": datum["input"]["left"],
                    "metadata": datum["metadata"],
                    "metadata.operator": datum["metadata"]["operator"],
                    "operator": datum["input"]["operator"],
                    "right": datum["input"]["right"],
                },
            },
        )
        self.assertEqual(
            scorer_span["metadata"]["prompt"]["variables"],
            {
                "expected": datum["expected"],
                "input": datum["input"],
                "metadata": datum["metadata"],
                "output": prompt_output,
            },
        )

    @parameterized.expand([(package,) for package in ["re", "autoevals", "braintrust", "requests", "openai"]])
    def test_invoke_with_package(self, package):
        res = self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/invoke",
            json={
                "code": f"""import {package}
def handler(output, expected):
  return 1
""",
                "inline_context": {"runtime": "python", "version": "3.12"},
                "input": {"output": "", "expected": ""},
            },
        ).json()
        self.assertEqual(res, 1)

    def test_fetch_js(self):
        res = self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/invoke",
            json={
                "code": """\
async function handler({
  output,
  expected
}: {
  output: any;
  expected: any;
}): number {
  const headers = new Headers({ Authorization: "Bearer %s" })
  const req = new Request("%s/v1/organization/%s", { headers });
  const res: Response = await fetch(req);
  return res.status == 200 ? 1 : 0;
}
"""
                % (self.org_api_key, LOCAL_API_URL, self.org_id),
                "inline_context": {"runtime": "node", "version": "20"},
                "input": {"output": "", "expected": ""},
            },
        ).json()
        self.assertEqual(res, 1)

    def test_invoke_py_code_with_unset_keyword(self):
        res = self.run_request(
            "post",
            f"{LOCAL_API_URL}/function/invoke",
            json={
                "code": """\
def handler(output, expected):
  return expected
""",
                "inline_context": {"runtime": "python", "version": "3.12"},
                "input": {"input": "input", "output": "output"},
            },
        ).json()
        self.assertIsNone(res)

    def test_invoke_with_base64_image(self):
        # This test depends on attachments being set up.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("")

        slug = "image-prompt"
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "system", "content": "What is the image?"},
                        {
                            "role": "user",
                            "content": [{"type": "image_url", "image_url": {"url": "{{image_url}}"}}],
                        },
                    ],
                },
                "options": {
                    "model": "gpt-4o-mini",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug=slug,
        )

        base64_image = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII="
        logger = braintrust.init_logger("image-logging")

        resp = braintrust.invoke(
            project_name=self.project.name, slug=slug, input={"image_url": base64_image}, parent=logger.export()
        )

        logs = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(logs), 2)
        for log in logs:
            self.assertTrue("braintrust_attachment" in json.dumps(log["input"]))

    def test_invoke_with_attachment(self):
        # This test depends on attachments being set up.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("")

        if not TEST_ARGS.get("update"):
            raise unittest.SkipTest("This test can only run without the cache because its payload is dynamic")

        dataset = braintrust.init_dataset(self.project.name, "attachments")
        base64_image = base64.b64decode(
            "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII="
        )
        attachment = braintrust.Attachment(
            data=base64_image,
            filename="foo.png",
            content_type="image/png",
        )
        dataset.insert(input={"img": attachment})
        dataset.flush()

        slug = "image-prompt"
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "system", "content": "What is the image?"},
                        {
                            "role": "user",
                            "content": [{"type": "image_url", "image_url": {"url": "{{img}}"}}],
                        },
                    ],
                },
                "options": {
                    "model": "gpt-4o-mini",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="my prompt",
            slug=slug,
        )

        logger = braintrust.init_logger(self.project.name)
        dataset = braintrust.init_dataset(self.project.name, "attachments")
        reference = [x for x in dataset][0]["input"]["img"].reference

        resp = braintrust.invoke(
            # This is the internal representation of embedded attachments
            project_name=self.project.name,
            slug=slug,
            input={"img": json.dumps(reference)},
            parent=logger.export(),
        )

        self.run_request("post", f"{self.proxy_url}/debug/flush")
        logs = get_object_json("project_logs", logger.project.id)

        function_log = next((log for log in logs if log["span_attributes"]["type"] == "function"))
        self.assertTrue("heart" in function_log["output"])

        llm_log = next((log for log in logs if log["span_attributes"]["type"] == "llm"))
        self.assertEqual(llm_log["input"][1]["content"][0]["image_url"]["url"]["type"], "braintrust_attachment")

        resp = self.run_request(
            "post",
            f"{self.proxy_url}/function/eval",
            json={
                "project_id": self.project.id,
                "data": {"project_name": self.project.name, "dataset_name": "attachments"},
                "task": {
                    "function_id": function_record["id"],
                },
                "scores": [],
            },
        )

        logs = get_object_json("experiment", resp.json()["experimentId"])
        self.assertEqual(len(logs), 3)

        root_log = next((log for log in logs if log["is_root"]))
        self.assertEqual(root_log["input"]["img"]["type"], "braintrust_attachment")
        self.assertTrue("heart" in root_log["output"])

        task_log = next((log for log in logs if log["span_attributes"]["name"] == "task"))
        self.assertEqual(task_log["input"]["img"]["type"], "braintrust_attachment")
        self.assertTrue("heart" in task_log["output"])

        llm_log = next((log for log in logs if log["span_attributes"]["type"] == "llm"))
        self.assertEqual(llm_log["input"][1]["content"][0]["image_url"]["url"]["type"], "braintrust_attachment")

    def test_invoke_with_metadata_and_tags(self):
        """Test that invoke properly logs metadata and tags to the span"""
        slug = "metadata-tags-test"

        # Create a simple function that returns the input
        function_record = self._insert_function(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Return exactly: {{value}}"}],
                },
                "options": {
                    "model": "gpt-4o",
                },
            },
            function_data={
                "type": "prompt",
            },
            name="metadata tags test",
            slug=slug,
        )

        # Create a logger to capture the function call
        logger = braintrust.init_logger(self.project.name)

        # Test data
        test_input = {"value": "test response"}
        test_metadata = {"experiment_id": "exp_123", "model_version": "v1.0", "custom_field": "custom_value"}
        test_tags = ["test", "metadata", "invoke"]

        # Invoke the function with metadata and tags
        resp = braintrust.invoke(
            project_name=self.project.name,
            slug=slug,
            input=test_input,
            metadata=test_metadata,
            tags=test_tags,
            parent=logger.export(),
        )

        # Flush to ensure logs are written
        self.run_request("post", f"{self.proxy_url}/debug/flush")

        # Retrieve the logged spans
        logs = get_object_json("project_logs", logger.project.id)

        # Should have at least 2 logs: the function call and the LLM call
        self.assertGreaterEqual(len(logs), 2)

        # Find the function span
        function_log = next((log for log in logs if log["span_attributes"]["type"] == "function"), None)
        self.assertIsNotNone(function_log, "Function span not found in logs")

        # Verify metadata is logged correctly
        self.assertIsNotNone(function_log.get("metadata"), "Metadata field not found in function span")
        logged_metadata = function_log["metadata"]

        # Check that all test metadata fields are present
        for key, value in test_metadata.items():
            self.assertIn(key, logged_metadata, f"Metadata key '{key}' not found in logged metadata")
            self.assertEqual(logged_metadata[key], value, f"Metadata value for '{key}' doesn't match")

        # Verify tags are logged correctly
        self.assertIsNotNone(function_log.get("tags"), "Tags field not found in function span")
        logged_tags = function_log["tags"]

        # Check that all test tags are present
        for tag in test_tags:
            self.assertIn(tag, logged_tags, f"Tag '{tag}' not found in logged tags")

        # Verify the function executed correctly
        self.assertEqual(resp, "test response")

        # Verify input is logged correctly
        self.assertEqual(function_log["input"], test_input)


def parse_streaming_function_call(response):
    stream = BraintrustStream(sseclient.SSEClient(response))
    return stream.final_value()


def parse_streaming_eval_call(response):
    client = sseclient.SSEClient(response)
    metadata, summary = None, None
    for event in client.events():
        if event.event == "start":
            metadata = json.loads(event.data)
        elif event.event == "summary":
            summary = json.loads(event.data)
        elif event.event == "done":
            break
        else:
            raise ValueError(f"Unexpected event: {event.event}")
    return metadata, summary
