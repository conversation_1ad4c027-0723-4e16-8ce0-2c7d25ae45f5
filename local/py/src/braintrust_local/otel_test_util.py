import json
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, Iterable, List, Optional

import httpx
import requests
from braintrust import ObjectMetadata

from .trace import Span, span_to_prompt


def fetch_project_logs(api_url, api_key, project_id):
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {api_key}",
    }
    body = {
        "query": {
            "from": {
                "op": "function",
                "name": {"op": "ident", "name": ["project_logs"]},
                "args": [{"op": "literal", "value": project_id}],
                "shape": "traces",
            },
            "select": [{"op": "star"}],
        },
        "fmt": "json",
    }
    response = requests.post(f"{api_url}/btql", json=body, headers=headers)
    if not response.ok:
        raise Exception(f"Error fetching project logs: {response.status_code}: {response.text}")
    resp = response.json()
    return resp["data"]


def get_span_name(span):
    return span.get("span_attributes", {}).get("name")


@dataclass
class ExpectedSpan:
    name: str
    children: List["ExpectedSpan"] = field(default_factory=list)
    input: Optional[Any] = None
    output: Optional[Any] = None


@dataclass
class TestCase:
    test_name: str
    run_test: Callable[[Any], Any]
    expected_root_span: ExpectedSpan
    skip_prompt_check: bool = False
    skip_llm_spans_check: bool = False


def find_matching_span(
    *,
    test_name,
    spans,
    parent_span_id,
    expected_span,
):
    candidate_spans = []
    for s in spans:
        if not parent_span_id and not s.get("is_root"):
            continue
        if parent_span_id and parent_span_id not in (s.get("span_parents") or []):
            continue
        candidate_spans.append(s)

    def _does_span_match(span, expected_span):
        span_name = get_span_name(span)
        if span_name != expected_span.name:
            return False
        for child_expected_span in expected_span.children:
            child_span = find_matching_span(
                test_name=test_name,
                spans=spans,
                parent_span_id=span.get("span_id"),
                expected_span=child_expected_span,
            )
            if not child_span:
                span_id = span.get("span_id")
                raise Exception(f"No matching child span found for span {span_name} (expected_span {expected_span})")
        return True

    matching_span = None
    for span in candidate_spans:
        if _does_span_match(span, expected_span):
            matching_span = span
            break

    if not matching_span:
        candidate_spans_str = json.dumps(candidate_spans, indent=2)
        raise Exception(
            f"No matching span found for test case {test_name} (expected_span {expected_span}). Candidate spans: {candidate_spans_str}"
        )

    if expected_span.input:
        input = matching_span.get("input")
        if input != expected_span.input:
            raise Exception(f"Input mismatch for span {expected_span.name}: {input} != {expected_span.input}")

    if expected_span.output:
        output = matching_span.get("output")
        if output != expected_span.output:
            raise Exception(f"Output mismatch for span {expected_span.name}: {output} != {expected_span.output}")

    return matching_span


def get_http_client_with_capture_hook(
    captured_requests: List[httpx.Request],
    async_: bool,
):
    if async_:

        async def capture(request):
            captured_requests.append(request)

        return httpx.AsyncClient(
            event_hooks=dict(
                request=[capture],
            ),
        )
    else:

        def capture(request):
            captured_requests.append(request)

        return httpx.Client(
            event_hooks=dict(
                request=[capture],
            ),
        )


def request_to_prompt(request: httpx.Request):
    content = request.content
    content_str = content.decode("utf-8")
    return json.loads(content_str)


def remove_duplicates(lst):
    seen = set()
    unique_lst = []
    for item in lst:
        serialized = json.dumps(item, sort_keys=True)
        if serialized not in seen:
            seen.add(serialized)
            unique_lst.append(item)
    return unique_lst


def verify_test_outputs(
    api_url: str,
    api_key: str,
    test_cases: Iterable[TestCase],
    project: ObjectMetadata,
    captured_requests_by_test_name: Dict[str, httpx.Request],
    test_name_metadata_key: str,
):
    project_spans = fetch_project_logs(api_url, api_key, project.id)

    # Order the spans from oldest to newest, using the `start` OTEL span attribute.
    project_spans.sort(key=lambda s: s["metrics"]["start"])
    print(f"Fetched {len(project_spans)} spans for project: {project.name}")

    def _summarize_span(s, level=0):
        span_id = s.get("span_id")
        root_span_id = s.get("root_span_id")
        span_parents = s.get("span_parents")
        span_name = get_span_name(s)
        children = [c for c in project_spans if span_id in (c.get("span_parents") or [])]

        indent = "   " * level + "|--" if level > 0 else ""
        summary = " ".join(
            [
                indent,
                f"[{span_name}]",
                f"<{span_id}>",
                f"parents: {span_parents}\n",
            ]
        )
        child_summaries = "".join([_summarize_span(c, level=level + 1) for c in children])
        return summary + child_summaries

    spans_by_test_name = {}
    for span in project_spans:
        root_span_id = span.get("root_span_id")
        if not root_span_id:
            raise Exception(f"No root_span_id found for span: {span}")

        if span.get("is_root"):
            print(f"< root_span_id: {root_span_id} >")
            print(_summarize_span(span))

        test_name = span.get("metadata", {}).get(test_name_metadata_key)
        if not test_name:
            raise Exception(f"No test name found in metadata for span: {span}")

        if test_name not in spans_by_test_name:
            spans_by_test_name[test_name] = []
        spans_by_test_name[test_name].append(span)

    for tc in test_cases:
        spans = spans_by_test_name.get(tc.test_name)
        if not spans:
            raise Exception(f"No spans found for test case '{tc.test_name}'")

        expected_root_span = tc.expected_root_span
        root_span = find_matching_span(
            test_name=tc.test_name,
            spans=spans,
            parent_span_id=None,
            expected_span=expected_root_span,
        )
        if not root_span:
            raise Exception(f"No matching root span found for test case '{tc.test_name}'")

        if tc.skip_llm_spans_check:
            print(f"Skipping LLM spans check for test case '{tc.test_name}'")
            continue

        llm_spans = [s for s in spans if s.get("span_attributes", {}).get("type") == "llm"]
        if len(llm_spans) == 0:
            raise Exception(f"No spans of type 'llm' were found for test case '{tc.test_name}'")

        captured_requests = captured_requests_by_test_name[tc.test_name]

        # NOTE(austin): Check for and delete duplicates here because sometimes
        # the telemetry SDKs fire off duplicate requests for whatever reason.
        captured_prompts = remove_duplicates([request_to_prompt(r) for r in captured_requests])

        if len(llm_spans) != len(captured_prompts):
            raise Exception(f"Number of reconstructed prompts did not match # of captured LLM calls")

        if not tc.skip_prompt_check:
            for i, row in enumerate(llm_spans):
                captured_prompt = captured_prompts[i]
                # We don't populate this field for now so remove it before comparing.
                captured_prompt.pop("stream", None)

                reconstructed_prompt = span_to_prompt(Span.from_dict_deep(row))
                if not reconstructed_prompt:
                    raise Exception(f"Failed to parse span and convert to completion args: {row}")

                captured_prompt = json.dumps(captured_prompt, sort_keys=True)
                reconstructed_prompt = json.dumps(reconstructed_prompt, sort_keys=True)
                if captured_prompt != reconstructed_prompt:
                    raise Exception(f"Reconstructed prompt for '{tc.test_name}' did not match captured LLM call")

        print(f"Test case '{tc.test_name}' passed.")


if __name__ == "__main__":
    main()
